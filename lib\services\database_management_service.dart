import 'package:flutter/material.dart';
import '../helpers/database_helper.dart';
import '../models/database_table_model.dart';

/// خدمة إدارة قاعدة البيانات
/// 
/// توفر واجهة عالية المستوى لإدارة قاعدة البيانات
class DatabaseManagementService {
  final DatabaseHelper _databaseHelper;

  DatabaseManagementService(this._databaseHelper);

  /// الحصول على قائمة الجداول المتاحة
  Future<List<DatabaseTable>> getAvailableTables() async {
    try {
      final tableNames = _databaseHelper.tableNames;
      final tables = <DatabaseTable>[];

      for (final tableName in tableNames) {
        final table = await _createTableModel(tableName);
        if (table != null) {
          tables.add(table);
        }
      }

      // ترتيب الجداول حسب الاسم المعروض
      tables.sort((a, b) => a.displayName.compareTo(b.displayName));

      debugPrint('تم إنشاء ${tables.length} نموذج جدول من ${tableNames.length} جدول');
      return tables;
    } catch (e) {
      debugPrint('خطأ في الحصول على الجداول المتاحة: $e');
      rethrow; // إعادة رمي الخطأ للمتحكم
    }
  }

  /// الحصول على بيانات جدول محدد
  Future<List<Map<String, dynamic>>> getTableData(
    String tableName, {
    int page = 1,
    int pageSize = 50,
    String? searchQuery,
    Map<String, dynamic>? filters,
  }) async {
    return await _databaseHelper.getTableData(
      tableName,
      page: page,
      pageSize: pageSize,
      searchQuery: searchQuery,
      filters: filters,
    );
  }

  /// إنشاء سجل جديد في جدول
  Future<Map<String, dynamic>?> createRecord(
    String tableName,
    Map<String, dynamic> data,
  ) async {
    return await _databaseHelper.createRecord(tableName, data);
  }

  /// تحديث سجل في جدول
  Future<Map<String, dynamic>?> updateRecord(
    String tableName,
    int id,
    Map<String, dynamic> data,
  ) async {
    return await _databaseHelper.updateRecord(tableName, id, data);
  }

  /// حذف سجل من جدول
  Future<bool> deleteRecord(String tableName, int id) async {
    return await _databaseHelper.deleteRecord(tableName, id);
  }

  /// الحصول على معلومات قاعدة البيانات
  Map<String, dynamic> getDatabaseInfo() {
    return _databaseHelper.databaseInfo;
  }

  /// اختبار الاتصال بقاعدة البيانات
  Future<bool> testConnection() async {
    return await _databaseHelper.testConnection();
  }

  /// إنشاء نموذج جدول
  Future<DatabaseTable?> _createTableModel(String tableName) async {
    try {
      final columns = <DatabaseColumn>[];
      int recordCount = 0;

      try {
        // محاولة الحصول على عينة من البيانات لتحديد الأعمدة
        final sampleData = await _databaseHelper.getTableData(
          tableName,
          page: 1,
          pageSize: 1,
        );

        if (sampleData.isNotEmpty) {
          final firstRow = sampleData.first;
          recordCount = sampleData.length; // تقدير أولي

          for (final entry in firstRow.entries) {
            final column = DatabaseColumn(
              name: entry.key,
              displayName: _getColumnDisplayName(entry.key),
              type: _getColumnType(entry.value),
              isNullable: entry.value == null,
              isPrimaryKey: _isPrimaryKeyColumn(entry.key),
              isRequired: _isRequiredColumn(entry.key),
              isAutoIncrement: _isAutoIncrementColumn(entry.key),
              isEditable: !entry.key.toLowerCase().contains('createdat') &&
                         !entry.key.toLowerCase().contains('updatedat'),
              isVisibleInList: _isVisibleInList(entry.key),
              isSearchable: _isSearchableColumn(entry.key),
              isSortable: true,
            );
            columns.add(column);
          }
        } else {
          // إذا لم تكن هناك بيانات، استخدم الأعمدة الافتراضية
          columns.addAll(_getDefaultColumns(tableName));
        }
      } catch (e) {
        // في حالة فشل تحميل البيانات، استخدم الأعمدة الافتراضية
        debugPrint('فشل في تحميل بيانات الجدول $tableName، استخدام الأعمدة الافتراضية: $e');
        columns.addAll(_getDefaultColumns(tableName));
      }

      return DatabaseTable(
        name: tableName,
        displayName: _getTableDisplayName(tableName),
        columns: columns,
        recordCount: recordCount,
        description: _getTableDescription(tableName),
        isEditable: _isTableEditable(tableName),
        isDeletable: _isTableDeletable(tableName),
        isCreatable: _isTableCreatable(tableName),
        isExportable: true,
        isImportable: _isTableImportable(tableName),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء نموذج الجدول $tableName: $e');
      return null;
    }
  }

  /// تحديد نوع العمود بناءً على القيمة
  DatabaseColumnType _getColumnType(dynamic value) {
    if (value == null) return DatabaseColumnType.text;
    
    if (value is int) return DatabaseColumnType.integer;
    if (value is double) return DatabaseColumnType.decimal;
    if (value is bool) return DatabaseColumnType.boolean;
    if (value is DateTime) return DatabaseColumnType.datetime;
    
    // فحص إذا كانت النص يحتوي على تاريخ
    if (value is String) {
      if (value.contains('@')) return DatabaseColumnType.email;
      if (value.startsWith('http')) return DatabaseColumnType.url;
      if (RegExp(r'^\d{4}-\d{2}-\d{2}').hasMatch(value)) {
        return DatabaseColumnType.datetime;
      }
    }
    
    return DatabaseColumnType.text;
  }

  /// الحصول على الأعمدة الافتراضية للجدول
  List<DatabaseColumn> _getDefaultColumns(String tableName) {
    final commonColumns = [
      DatabaseColumn(
        name: 'Id',
        type: DatabaseColumnType.integer,
        isNullable: false,
        isPrimaryKey: true,
        isRequired: false, // تغيير إلى false لأن الأعمدة التي تزيد تلقائياً لا تكون مطلوبة
        isAutoIncrement: true, // إضافة هذه الخاصية
      ),
    ];

    switch (tableName.toLowerCase()) {
      case 'users':
        commonColumns.addAll([
          DatabaseColumn(name: 'Name', type: DatabaseColumnType.text, isRequired: true),
          DatabaseColumn(name: 'FirstName', type: DatabaseColumnType.text),
          DatabaseColumn(name: 'LastName', type: DatabaseColumnType.text),
          DatabaseColumn(name: 'Email', type: DatabaseColumnType.email, isRequired: true),
          DatabaseColumn(name: 'Username', type: DatabaseColumnType.text, isRequired: true),
          DatabaseColumn(name: 'Role', type: DatabaseColumnType.integer),
          DatabaseColumn(name: 'DepartmentId', type: DatabaseColumnType.integer),
          DatabaseColumn(name: 'IsActive', type: DatabaseColumnType.boolean),
          DatabaseColumn(name: 'CreatedAt', type: DatabaseColumnType.datetime),
        ]);
        break;
      case 'departments':
        commonColumns.addAll([
          DatabaseColumn(name: 'Name', type: DatabaseColumnType.text, isRequired: true),
          DatabaseColumn(name: 'Description', type: DatabaseColumnType.text),
          DatabaseColumn(name: 'ManagerId', type: DatabaseColumnType.integer),
          DatabaseColumn(name: 'IsActive', type: DatabaseColumnType.boolean),
          DatabaseColumn(name: 'CreatedAt', type: DatabaseColumnType.datetime),
        ]);
        break;
      case 'tasks':
        commonColumns.addAll([
          DatabaseColumn(name: 'Title', type: DatabaseColumnType.text, isRequired: true),
          DatabaseColumn(name: 'Description', type: DatabaseColumnType.text),
          DatabaseColumn(name: 'CreatorId', type: DatabaseColumnType.integer),
          DatabaseColumn(name: 'AssigneeId', type: DatabaseColumnType.integer),
          DatabaseColumn(name: 'DepartmentId', type: DatabaseColumnType.integer),
          DatabaseColumn(name: 'Status', type: DatabaseColumnType.integer),
          DatabaseColumn(name: 'Priority', type: DatabaseColumnType.integer),
          DatabaseColumn(name: 'CompletionPercentage', type: DatabaseColumnType.integer),
          DatabaseColumn(name: 'EstimatedTime', type: DatabaseColumnType.integer),
          DatabaseColumn(name: 'StartDate', type: DatabaseColumnType.datetime),
          DatabaseColumn(name: 'DueDate', type: DatabaseColumnType.datetime),
          DatabaseColumn(name: 'CreatedAt', type: DatabaseColumnType.datetime),
        ]);
        break;
      default:
        commonColumns.addAll([
          DatabaseColumn(name: 'Name', type: DatabaseColumnType.text),
          DatabaseColumn(name: 'CreatedAt', type: DatabaseColumnType.datetime),
          DatabaseColumn(name: 'UpdatedAt', type: DatabaseColumnType.datetime),
        ]);
    }

    return commonColumns;
  }

  /// الحصول على الاسم المعروض للجدول
  String _getTableDisplayName(String tableName) {
    switch (tableName.toLowerCase()) {
      case 'users':
        return 'المستخدمون';
      case 'departments':
        return 'الأقسام';
      case 'tasks':
        return 'المهام';
      case 'taskstatuses':
        return 'حالات المهام';
      case 'taskpriorities':
        return 'أولويات المهام';
      case 'tasktypes':
        return 'أنواع المهام';
      case 'taskcomments':
        return 'تعليقات المهام';
      case 'taskhistories':
        return 'تاريخ المهام';
      case 'subtasks':
        return 'المهام الفرعية';
      case 'permissions':
        return 'الصلاحيات';
      case 'userpermissions':
        return 'صلاحيات المستخدمين';
      case 'archivecategories':
        return 'فئات الأرشيف';
      case 'archivedocuments':
        return 'وثائق الأرشيف';
      case 'archivetags':
        return 'علامات الأرشيف';
      case 'archivedocumenttags':
        return 'روابط الوثائق والعلامات';
      case 'attachments':
        return 'المرفقات';
      case 'backups':
        return 'النسخ الاحتياطية';
      case 'calendarevents':
        return 'أحداث التقويم';
      case 'chatgroups':
        return 'مجموعات الدردشة';
      case 'messages':
        return 'الرسائل';
      case 'notifications':
        return 'الإشعارات';
      case 'reports':
        return 'التقارير';
      case 'systemlogs':
        return 'سجلات النظام';
      case 'systemsettings':
        return 'إعدادات النظام';
      case 'timetrackingentries':
        return 'إدخالات تتبع الوقت';
      case 'activitylogs':
        return 'سجلات النشاط';
      case 'dashboards':
        return 'لوحات المعلومات';
      case 'dashboardwidgets':
        return 'عناصر لوحة المعلومات';
      default:
        return tableName;
    }
  }

  /// الحصول على الاسم المعروض للعمود
  String _getColumnDisplayName(String columnName) {
    switch (columnName.toLowerCase()) {
      case 'id':
        return 'المعرف';
      case 'name':
        return 'الاسم';
      case 'firstname':
        return 'الاسم الأول';
      case 'lastname':
        return 'الاسم الأخير';
      case 'email':
        return 'البريد الإلكتروني';
      case 'username':
        return 'اسم المستخدم';
      case 'password':
        return 'كلمة المرور';
      case 'role':
        return 'الدور';
      case 'departmentid':
        return 'القسم';
      case 'isactive':
        return 'نشط';
      case 'isdeleted':
        return 'محذوف';
      case 'createdat':
        return 'تاريخ الإنشاء';
      case 'updatedat':
        return 'تاريخ التحديث';
      case 'title':
        return 'العنوان';
      case 'description':
        return 'الوصف';
      case 'status':
        return 'الحالة';
      case 'priority':
        return 'الأولوية';
      case 'startdate':
        return 'تاريخ البداية';
      case 'duedate':
        return 'تاريخ الاستحقاق';
      case 'completionpercentage':
        return 'نسبة الإنجاز';
      case 'estimatedtime':
        return 'الوقت المقدر';
      case 'creatorid':
        return 'المنشئ';
      case 'assigneeid':
        return 'المكلف';
      default:
        return columnName;
    }
  }

  /// تحديد ما إذا كان العمود مفتاح أساسي
  bool _isPrimaryKeyColumn(String columnName) {
    final lowerName = columnName.toLowerCase();
    final displayName = _getColumnDisplayName(columnName).toLowerCase();

    return lowerName == 'id' ||
           lowerName == 'المعرف' ||
           displayName == 'المعرف' ||
           columnName == 'المعرف' ||
           (lowerName.endsWith('id') && lowerName.length <= 3) ||
           lowerName.contains('معرف') ||
           displayName.contains('معرف');
  }

  /// تحديد ما إذا كان العمود يزيد تلقائياً
  bool _isAutoIncrementColumn(String columnName) {
    final lowerName = columnName.toLowerCase();
    final displayName = _getColumnDisplayName(columnName).toLowerCase();

    // الأعمدة التي تزيد تلقائياً عادة ما تكون مفاتيح أساسية
    final isAutoIncrement = lowerName == 'id' ||
           lowerName == 'المعرف' ||
           displayName == 'المعرف' ||
           columnName == 'المعرف' ||
           (lowerName.endsWith('id') && lowerName.length <= 3) ||
           // إضافة فحص للأعمدة التي تحتوي على كلمة "معرف"
           lowerName.contains('معرف') ||
           displayName.contains('معرف');

    // طباعة تشخيصية لمساعدة في التشخيص
    if (isAutoIncrement) {
      debugPrint('تم تحديد العمود "$columnName" (عرض: "$displayName") كعمود يزيد تلقائياً');
    }

    return isAutoIncrement;
  }

  /// تحديد ما إذا كان العمود مطلوباً
  bool _isRequiredColumn(String columnName) {
    final lowerName = columnName.toLowerCase();
    // الأعمدة التي تزيد تلقائياً لا تكون مطلوبة من المستخدم
    if (_isAutoIncrementColumn(columnName)) {
      return false;
    }
    return lowerName.contains('name') ||
           lowerName.contains('title') ||
           lowerName.contains('email');
  }

  /// تحديد ما إذا كان العمود مرئياً في القائمة
  bool _isVisibleInList(String columnName) {
    final lowerName = columnName.toLowerCase();
    // إخفاء كلمات المرور والحقول الحساسة
    return !lowerName.contains('password') &&
           !lowerName.contains('token') &&
           !lowerName.contains('secret');
  }

  /// تحديد ما إذا كان العمود قابلاً للبحث
  bool _isSearchableColumn(String columnName) {
    final lowerName = columnName.toLowerCase();
    return lowerName.contains('name') ||
           lowerName.contains('title') ||
           lowerName.contains('email') ||
           lowerName.contains('description');
  }

  /// الحصول على وصف الجدول
  String? _getTableDescription(String tableName) {
    switch (tableName.toLowerCase()) {
      case 'users':
        return 'إدارة المستخدمين وحساباتهم';
      case 'departments':
        return 'إدارة الأقسام والوحدات التنظيمية';
      case 'tasks':
        return 'إدارة المهام والأنشطة';
      case 'taskstatuses':
        return 'حالات المهام المختلفة';
      case 'taskpriorities':
        return 'مستويات أولوية المهام';
      case 'permissions':
        return 'صلاحيات النظام';
      case 'archivecategories':
        return 'إدارة فئات الأرشيف الإلكتروني';
      case 'archivedocuments':
        return 'إدارة وثائق الأرشيف الإلكتروني';
      case 'archivetags':
        return 'إدارة علامات الأرشيف الإلكتروني';
      case 'archivedocumenttags':
        return 'إدارة روابط الوثائق بالعلامات';
      case 'notifications':
        return 'إشعارات النظام';
      case 'reports':
        return 'التقارير والإحصائيات';
      default:
        return null;
    }
  }

  /// تحديد ما إذا كان الجدول قابلاً للتعديل
  bool _isTableEditable(String tableName) {
    final lowerName = tableName.toLowerCase();
    // بعض الجداول قد تكون للقراءة فقط
    return !lowerName.contains('log') && !lowerName.contains('history');
  }

  /// تحديد ما إذا كان الجدول قابلاً للحذف
  bool _isTableDeletable(String tableName) {
    final lowerName = tableName.toLowerCase();
    // بعض الجداول الحساسة لا يجب حذف بياناتها
    return !lowerName.contains('log') &&
           !lowerName.contains('history') &&
           lowerName != 'permissions';
  }

  /// تحديد ما إذا كان الجدول قابلاً لإنشاء سجلات جديدة
  bool _isTableCreatable(String tableName) {
    final lowerName = tableName.toLowerCase();
    // بعض الجداول قد تكون للقراءة فقط
    return !lowerName.contains('log') && !lowerName.contains('history');
  }

  /// تحديد ما إذا كان الجدول قابلاً للاستيراد
  bool _isTableImportable(String tableName) {
    final lowerName = tableName.toLowerCase();
    // السماح بالاستيراد للجداول الأساسية فقط
    return lowerName == 'users' ||
           lowerName == 'departments' ||
           lowerName == 'tasks';
  }

  /// إعادة تحميل البيانات
  Future<void> refresh() async {
    await _databaseHelper.refresh();
  }

  /// الحصول على خيارات المفتاح الخارجي
  Future<List<Map<String, dynamic>>> getForeignKeyOptions(
    String tableName,
    String keyColumn,
    String displayColumn,
  ) async {
    try {
      final data = await _databaseHelper.getTableData(tableName, pageSize: 1000);
      return data.map((row) => {
        keyColumn: row[keyColumn],
        displayColumn: row[displayColumn] ?? row[keyColumn],
      }).toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على خيارات المفتاح الخارجي: $e');
      return [];
    }
  }

  /// الحصول على قيمة عرض المفتاح الخارجي
  Future<String> getForeignKeyDisplayValue(
    String tableName,
    String keyColumn,
    String displayColumn,
    dynamic keyValue,
  ) async {
    try {
      final data = await _databaseHelper.getTableData(tableName, pageSize: 1000);
      final row = data.firstWhere(
        (row) => row[keyColumn].toString() == keyValue.toString(),
        orElse: () => {},
      );

      if (row.isNotEmpty) {
        return row[displayColumn]?.toString() ?? keyValue.toString();
      }

      return keyValue.toString();
    } catch (e) {
      debugPrint('خطأ في الحصول على قيمة عرض المفتاح الخارجي: $e');
      return keyValue.toString();
    }
  }
}
